{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a slideshow section.

  Accepts:
  - full_width {boolean} - Whether to make the section full width
  - autoplay {boolean} - Whether to autoplay the slideshow
  - autoplay_speed {5-10} - The speed at which the slideshow should autoplay
  - style {'minimal'|'arrows'|'bars'|'dots'} - The style of the slideshow
  - desktop_size {-100-100} - The size of the desktop image
  - mobile_size {-100-100} - The size of the mobile image
  - hydration {string} - The hydration strategy for the section

  Usage:
  {% render 'section-slideshow-banner' %}
{%- endcomment -%}

{%- liquid
  assign full_width = full_width | default: section.settings.full_width, allow_false: true | default: true, allow_false: true
  assign autoplay = autoplay | default: section.settings.autoplay, allow_false: true | default: true, allow_false: true
  assign autoplay_speed = autoplay_speed | default: section.settings.autoplay_speed | default: 7
  assign style = style | default: section.settings.style | default: 'arrows'
  assign desktop_size = desktop_size | default: section.settings.desktop_size | default: 76
  assign mobile_size = mobile_size | default: section.settings.mobile_size | default: 35
  assign hydration = hydration | default: 'on:visible'

  assign lazyload_images = true
  if section.index == 1
    assign lazyload_images = false
  endif
-%}

<style>
  #SlideshowWrapper-{{ section.id }} .flickity-button {
    top: 50% !important;
    width: 60px !important;
    height: 60px !important;
    background: transparent !important;
    border: none !important;
    border-radius: 50% !important;
    padding: 0 !important;
  }
  #SlideshowWrapper-{{ section.id }} .flickity-previous {
    left: 50px !important;
  }
  #SlideshowWrapper-{{ section.id }} .flickity-next {
    right: 50px !important;
  }

  /* 隐藏原始的 SVG 图标 */
  #SlideshowWrapper-{{ section.id }} .flickity-button-icon {
    /*display: none !important;*/
  }

  /* SVG 样式 */
  #SlideshowWrapper-{{ section.id }} .flickity-button svg {
    width: 60px;
    height: 60px;
    display: block;
  }

  /* 导航按钮SVG样式 */
  /* 默认状态（能点击） */
  #SlideshowWrapper-{{ section.id }} .flickity-button svg circle {
    fill: #ffffff; /* 背景颜色 */
    fill-opacity: 1; /* 确保不透明 */
    stroke: none; /* 边框颜色 */
    transition: fill 0.3s ease, stroke 0.3s ease;
  }

  #SlideshowWrapper-{{ section.id }} .flickity-button svg path {
    stroke: #6D4C41; /* 箭头颜色 */
    transition: stroke 0.3s ease;
  }

  /* 禁用状态（不能点击） */
  #SlideshowWrapper-{{ section.id }} .flickity-button:disabled svg circle {
    fill: #ffffff !important; /* 背景颜色，确保不透明 */
    fill-opacity: 1 !important; /* 确保完全不透明 */
    stroke: #CCCCCC !important; /* 边框颜色 */
  }

  #SlideshowWrapper-{{ section.id }} .flickity-button:disabled svg path {
    stroke: #CCCCCC; /* 箭头颜色 */
  }

  /* 悬停状态（鼠标放上去） */
  #SlideshowWrapper-{{ section.id }} .flickity-button:not(:disabled):hover svg circle {
    fill: #6D4C41; /* 背景颜色 */
    fill-opacity: 1; /* 确保不透明 */
    stroke: #6D4C41; /* 边框颜色保持一致 */
  }

  #SlideshowWrapper-{{ section.id }} .flickity-button:not(:disabled):hover svg path {
    stroke: #ffffff; /* 箭头颜色 */
  }

  /* 确保禁用状态的按钮完全不透明 */
  #SlideshowWrapper-{{ section.id }} .flickity-button:disabled {
    opacity: 1;
    background: transparent; /* 移除背景色，让SVG样式生效 */
  }

  /* 自定义分页样式 - 增加特异性以覆盖默认样式 */
  #SlideshowWrapper-{{ section.id }} .hero .flickity-page-dots {
    bottom: 20px !important;
    color: #FFFFFF !important;
  }

  #SlideshowWrapper-{{ section.id }} .hero .flickity-page-dots .dot {
    width: 14px !important;
    height: 14px !important;
    border-radius: 50% !important;
    background-color: #FFFFFF50 !important;
    opacity: 1 !important;
    margin: 0 5px !important;
    transition: background-color 0.3s ease !important;
    border: none !important;
  }

  #SlideshowWrapper-{{ section.id }} .hero .flickity-page-dots .dot.is-selected {
    background-color: #6D4C41 !important;
    width: 14px !important;
    height: 14px !important;
    opacity: 1 !important;
  }

  #SlideshowWrapper-{{ section.id }} .hero .flickity-page-dots .dot:hover {
    opacity: 1 !important;
    background-color: #FFFFFF !important;
  }

  #SlideshowWrapper-{{ section.id }} .hero .flickity-page-dots .dot:hover.is-selected {
    background-color: #6D4C41 !important;
  }

  /* 移动端分页样式 */
  @media screen and (max-width: 768px) {
    #SlideshowWrapper-{{ section.id }} .flickity-button {
      display: none !important;
    }

    #SlideshowWrapper-{{ section.id }} .hero .flickity-page-dots {
      bottom: 10px !important;
    }

    #SlideshowWrapper-{{ section.id }} .hero .flickity-page-dots .dot {
      width: 8px !important;
      height: 8px !important;
    }

    #SlideshowWrapper-{{ section.id }} .hero .flickity-page-dots .dot.is-selected {
      width: 8px !important;
      height: 8px !important;
    }
  }
</style>

<is-land {{ hydration }}>
  <slideshow-section section-id="{{ section.id }}">
    {%- unless full_width -%}
      <div class="page-width hero--padded">
    {%- endunless -%}
    <div id="SlideshowWrapper-{{ section.id }}">
      {%- if section.blocks.size > 0 -%}
        <div class="slideshow-wrapper">
          {%- if autoplay and style == 'bars' and section.blocks.size > 1 -%}
            {%- style -%}
              [data-bars][data-autoplay="true"] .flickity-page-dots .dot:after {
                animation-duration: {{ autoplay_speed | times: 1000 }}ms;
              }
            {%- endstyle -%}

            <button
              type="button"
              class="visually-hidden slideshow__pause"
              data-id="{{ section.id }}"
              aria-live="polite"
            >
              <span class="slideshow__pause-stop">
                {% render 'icon', name: 'pause' %}
                <span class="icon__fallback-text visually-hidden">
                  {% render 't_with_fallback', key: 'actions.slideshow_pause', fallback: 'Slideshow pause' -%}
                </span>
              </span>
              <span class="slideshow__pause-play">
                {% render 'icon', name: 'play' %}
                <span class="icon__fallback-text visually-hidden">
                  {% render 't_with_fallback', key: 'actions.slideshow_play', fallback: 'Slideshow play' -%}
                </span>
              </span>
            </button>
          {%- endif -%}

          {%- liquid
            assign natural_height_ratio = '60%'
            assign natural_height_ratio_mobile = '100%'
            for block in section.blocks
              if block.settings.image != blank
                assign natural_height_ratio = 100 | divided_by: block.settings.image.aspect_ratio | append: '%'
                assign natural_height_ratio_mobile = natural_height_ratio
              endif
              if block.settings.image_mobile != blank
                assign natural_height_ratio_mobile = 100 | divided_by: block.settings.image_mobile.aspect_ratio | append: '%'
              endif
            endfor
          -%}

          {%- style -%}
            .hero-natural--{{ section.id }} {
              height: 0;
              padding-bottom:  calc({{ desktop_size }}px * 10);
            }

            @media screen and (max-width: 768px) {
              .hero-natural--{{ section.id }} {
                padding-bottom: calc({{ mobile_size }}px * 10);
              }
            }
          {%- endstyle -%}

          <div class="hero-natural--{{ section.id }}">
            <div
              id="Slideshow-{{ section.id }}"
              class="hero hero--{{ section.id }}{% if section.index == 1 %} loaded{% else %} loading{% endif %} loading--delayed"
              data-natural="true"
              data-autoplay="{{ autoplay }}"
              data-speed="{{ autoplay_speed | times: 1000 }}"
              {% if style == 'arrows' %}
                data-arrows="true"
                data-dots="true"
              {% endif %}
              {% if style == 'dots' %}
                data-dots="true"
              {% endif %}
              {% if style == 'bars' %}
                data-dots="true"
                data-bars="true"
              {% endif %}
              data-slide-count="{{ section.blocks.size }}"
            >
              {%- for block in section.blocks -%}
                <div
                  {{ block.shopify_attributes }}
                  class="slideshow__slide slideshow__slide--{{ block.id }}{% if section.index == 1 and forloop.index == 1 %} is-selected{% endif %}"
                  data-index="{{ forloop.index0 }}"
                  data-id="{{ block.id }}"
                >
                  {%- style -%}
                    .slideshow__slide--{{ block.id }} .hero__title,
                    .slideshow__slide--{{ block.id }} .hero__title p {
                      font-size: {{ block.settings.title_size | times: 0.43 }}px;
                    }
                    @media only screen and (min-width: 769px) {
                      .slideshow__slide--{{ block.id }} .hero__title,
                      .slideshow__slide--{{ block.id }} .hero__title p {
                        font-size: {{ block.settings.title_size }}px;
                      }
                    }

                    {%- assign button_alpha = block.settings.color_accent | color_extract: 'alpha' -%}
                    {% unless button_alpha == 0.0 %}
                      .slideshow__slide--{{ block.id }} .btn {
                        background: {{ block.settings.color_accent }} !important;
                        border: none;

                        {%- assign accent_brightness = block.settings.color_accent | color_extract: 'lightness' -%}

                        {% if accent_brightness > 40 %}
                          color: #000 !important;
                        {% endif %}
                      }
                    {% endunless %}

                    {% if block.settings.overlay_opacity > 0 %}
                      .slideshow__slide--{{ block.id }} .hero__image-wrapper:after {
                        content: '';
                        position: absolute;
                        left: 0;
                        right: 0;
                        top: 0;
                        bottom: 0;
                        z-index: 3;
                        background-color: #000;
                        opacity: {{ block.settings.overlay_opacity | divided_by: 100.0 }};
                      }
                    {% endif %}

                    {% if block.settings.text_align == 'custom-position' %}
                      .slideshow__slide--{{ block.id }} .hero__text-wrap {
                        position: relative;
                      }
                      .slideshow__slide--{{ block.id }} .hero__text-content--custom {
                        position: absolute;
                        z-index: 4;
                        width: auto;
                        max-width: 90%;
                        left: {{ block.settings.button_position_x | default: 50 }}% !important;
                        top: {{ block.settings.button_position_y | default: 80 }}% !important;
                        transform: translate(-50%, -50%) !important;
                      }
                      .slideshow__slide--{{ block.id }} .hero__text-content--custom .hero__link {
                        text-align: center;
                      }

                      @media screen and (max-width: 768px) {
                        .slideshow__slide--{{ block.id }} .hero__text-content--custom {
                          left: {{ block.settings.button_position_x_mobile | default: 50 }}% !important;
                          top: {{ block.settings.button_position_y_mobile | default: 85 }}% !important;
                        }
                      }
                    {% endif %}
                  {%- endstyle -%}

                  {%- liquid
                    assign hero_text = false
                    if block.settings.top_subheading != blank or block.settings.title != blank or block.settings.subheading != blank or block.settings.link_text != blank
                      assign hero_text = true
                    endif

                    assign has_mobile_image = false
                    if block.settings.image_mobile != blank
                      assign has_mobile_image = true
                    endif
                  -%}

                  <div
                    class="hero__image-wrapper {% unless hero_text %} hero__image-wrapper--no-overlay{% endunless %}"
                  >
                    {% comment %} Full width images so don't need to adjust sizes attribute, fallback is 100vw {% endcomment %}
                    {%- liquid
                      if forloop.index == 1
                        assign loading = lazyload_images
                      else
                        assign loading = true
                      endif
                    -%}
                    {%- if block.settings.image != blank -%}
                      {% capture image_classes %}
                          hero__image hero__image--{{ block.id }}
                          {% if has_mobile_image %} small--hide{% endif %}
                        {% endcapture %}
                      {%- render 'image-element',
                        img: block.settings.image,
                        loading: loading,
                        classes: image_classes
                      -%}
                      {%- if has_mobile_image -%}
                        {% capture image_classes %}
                            hero__image hero__image--{{ block.id }} medium-up--hide
                          {% endcapture %}
                        {%- render 'image-element',
                          img: block.settings.image_mobile,
                          loading: loading,
                          classes: image_classes
                        -%}
                      {%- endif -%}
                    {%- else -%}
                      {%- if template == 'password' -%}
                        {%- assign password_image = 'password-page-background.jpg' -%}
                        {%- render 'image-element',
                          asset: password_image,
                          host: 'theme',
                          loading: lazyload_images,
                          type: 'asset',
                          classes: 'hero__image'
                        -%}
                      {%- else -%}
                        {%- render 'placeholder-svg', name: 'lifestyle-1' -%}
                      {%- endif -%}
                    {%- endif -%}
                  </div>

                  {%- if block.settings.link != blank and block.settings.link_2 == blank -%}
                    <a href="{{ block.settings.link }}" class="hero__slide-link" aria-hidden="true"></a>
                  {%- endif -%}

                  {%- if hero_text -%}
                    <div class="hero__text-wrap">
                      <div class="page-width">
                        {%- if block.settings.text_align == 'custom-position' -%}
                          <div class="hero__text-content hero__text-content--custom">
                        {%- else -%}
                          <div class="hero__text-content {{ block.settings.text_align }}">
                        {%- endif -%}
                          <div class="hero__text-shadow overlay">
                            {%- unless block.settings.top_subheading == blank -%}
                              <div class="hero__top-subtitle">
                                <div class="hero__animation-contents">
                                  {{ block.settings.top_subheading | escape }}
                                </div>
                              </div>
                            {%- endunless -%}
                            {%- unless block.settings.title == blank -%}
                              <h2
                                class="
                                  h1 hero__title
                                  {% if block.settings.text_highlight %}
                                    text-highlight
                                    text-highlight--{{ block.settings.text_highlight }}
                                  {% endif %}
                                "
                              >
                                <div class="hero__animation-contents">
                                  {{ block.settings.title | newline_to_br }}
                                </div>
                              </h2>
                            {%- endunless -%}
                            {%- if block.settings.subheading or block.settings.link or block.settings.link_2 -%}
                              {%- unless block.settings.subheading == blank -%}
                                <div class="hero__subtitle">
                                  <div class="hero__animation-contents">
                                    {{ block.settings.subheading | escape }}
                                  </div>
                                </div>
                              {%- endunless -%}
                              {%- if block.settings.link_text != blank or block.settings.link_text_2 != blank -%}
                                <div class="hero__link">
                                  {%- if block.settings.link_text != blank -%}
                                    <a
                                      href="{{ block.settings.link }}"
                                      class="btn{% if block.settings.color_accent contains 'rgba(0,0,0,0)' %} btn--inverse{% endif %}"
                                    >
                                      {{ block.settings.link_text }}
                                    </a>
                                  {%- endif -%}
                                  {%- if block.settings.link_text_2 != blank -%}
                                    <a
                                      href="{{ block.settings.link_2 }}"
                                      class="btn{% if block.settings.color_accent contains 'rgba(0,0,0,0)' %} btn--inverse{% endif %}"
                                    >
                                      {{ block.settings.link_text_2 }}
                                    </a>
                                  {%- endif -%}
                                </div>
                              {%- endif -%}
                            {%- endif -%}
                          </div>
                        </div>
                      </div>
                    </div>
                  {%- endif -%}
                </div>
              {%- endfor -%}
            </div>
          </div>
        </div>
      {%- endif -%}

      {%- render 'placeholder-noblocks' -%}
    </div>
    {%- unless full_width -%}
      </div>
    {%- endunless -%}
  </slideshow-section>

  <template data-island>
    <script type="module">
      import '@archetype-themes/modules/slideshow'

      // 添加自定义 SVG 图标到 Flickity 按钮
      document.addEventListener('DOMContentLoaded', function() {
        const slideshowWrapper = document.getElementById('SlideshowWrapper-{{ section.id }}');
        if (slideshowWrapper) {
          // 等待 Flickity 初始化完成
          setTimeout(() => {
            const prevButton = slideshowWrapper.querySelector('.flickity-previous');
            const nextButton = slideshowWrapper.querySelector('.flickity-next');

            // 左箭头 SVG
            const leftArrowSVG = `
              <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="30" cy="30" r="29.5" transform="matrix(-1 0 0 1 60 0)" fill="white" stroke="#6D4C41"/>
                <path d="M22.5 29.755H38" stroke="#6D4C41" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M30.25 22L22.5 29.75L30.25 37.5" stroke="#6D4C41" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            `;

            // 右箭头 SVG
            const rightArrowSVG = `
              <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="30" cy="30" r="29.5" fill="white" stroke="#6D4C41"/>
                <path d="M37.5 29.755H22" stroke="#6D4C41" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M29.75 22L37.5 29.75L29.75 37.5" stroke="#6D4C41" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            `;

            if (prevButton) {
              prevButton.innerHTML = leftArrowSVG;
            }
            if (nextButton) {
              nextButton.innerHTML = rightArrowSVG;
            }
          }, 100);
        }
      });
    </script>
  </template>
</is-land>
